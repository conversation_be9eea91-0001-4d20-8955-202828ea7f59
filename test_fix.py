#!/usr/bin/env python3
"""
YAA (YET ANOTHER AGENT) - Teste da correção
Testa se a correção do recent_api_results funcionou
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from collections import deque

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qualia.market.base_integration import CryptoDataFetcher

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class TestFWHScalpTrading:
    """Classe de teste simplificada para verificar a correção."""
    
    def __init__(self):
        # Simular a inicialização corrigida
        self.recent_api_results = {}  # Dicionário para diferentes tipos de API calls
        
    def _record_api_success(self, call_type: str, success: bool):
        """Registra o resultado de uma chamada da API"""
        if call_type not in self.recent_api_results:
            self.recent_api_results[call_type] = deque(maxlen=20)
        
        self.recent_api_results[call_type].append(success)
        print(f"✅ YAA-TEST: Registrado {call_type}: {success}")
        
    async def test_get_market_data(self, symbol: str, timeframe: str):
        """Testa o método _get_market_data corrigido."""
        print(f"🔍 YAA-TEST: Testando _get_market_data para {symbol} ({timeframe})")
        
        try:
            # Inicializar CryptoDataFetcher
            fetcher = CryptoDataFetcher(
                exchange_id="binance",
                api_key="",
                api_secret=""
            )
            
            await fetcher.initialize_connection()
            print("✅ YAA-TEST: CryptoDataFetcher inicializado")
            
            try:
                ticker = await fetcher.fetch_ticker(symbol)
            except Exception as exc:
                print(f"❌ YAA-TEST: Erro de conexão: {exc}")
                self._record_api_success('ticker', False)
                return None

            # Registrar sucesso da API call
            self._record_api_success('ticker', ticker is not None)

            if not ticker:
                print(f"⚠️ YAA-TEST: Nenhum ticker obtido para {symbol}")
                return None

            if not isinstance(ticker, dict):
                print(f"❌ YAA-TEST: Formato inesperado do ticker: {type(ticker)}")
                return None

            result = {
                'symbol': symbol,
                'price': float(ticker.get('last', 0)),
                'volume': float(ticker.get('baseVolume', 0)),
                'timestamp': datetime.now(),
                'bid': float(ticker.get('bid', 0)),
                'ask': float(ticker.get('ask', 0)),
                'change_24h': float(ticker.get('percentage', 0))
            }
            
            print(f"✅ YAA-TEST: Dados obtidos com sucesso!")
            print(f"📊 YAA-TEST: {result}")
            
            await fetcher.close()
            return result

        except Exception as e:
            print(f"❌ YAA-TEST: Erro geral: {e}")
            self._record_api_success('ticker', False)
            import traceback
            print(f"❌ YAA-TEST: Stack trace: {traceback.format_exc()}")
            return None

async def main():
    """Função principal de teste."""
    print("🚀 YAA-TEST: Iniciando teste da correção...")
    
    test_instance = TestFWHScalpTrading()
    
    # Testar com BTC/USDT
    result = await test_instance.test_get_market_data("BTC/USDT", "1m")
    
    if result:
        print("✅ YAA-TEST: Correção funcionou! Nenhum erro de indexação.")
    else:
        print("❌ YAA-TEST: Ainda há problemas.")
    
    print(f"📊 YAA-TEST: Estado do recent_api_results: {test_instance.recent_api_results}")

if __name__ == "__main__":
    asyncio.run(main())
